:host {

    .content {

        form {
            width: 100%;
            // max-width: 800px !important;
        }

        .form-errors-model {
            flex: 1;

            code {
                background: none !important;
            }
        }

        .horizontal-stepper-wrapper,
        .vertical-stepper-wrapper {
            max-width: 800px;
        }

        .mat-stroked-button.mat-small {

            height: 25px;
            line-height: 20px;
            min-height: 20px;
            vertical-align: middle !important;
            font-size: 10px;
            margin-bottom: 33%;
        }

        .mat-stroked-button.mat-sm {

            height: 25px;
            line-height: 20px;
            min-height: 20px;
            vertical-align: middle !important;
            font-size: 10px;
        }

        .mat-flat-button.mat-top-margin {
            margin-top: 20px !important;
        }

        .mat-stroked-button.mat-top-margin {
            margin-top: 20px !important;
        }

        .h-30 {
            height: 30px !important;
            min-height: 30px;
        }

        table {
            width: 100%;
        }

        .mat-form-field.grid-search-field {
            // font-size: 14px;
            width: 50%;
            margin-left: 20px !important;
        }

        .blue-snackbar {
            background: #2196F3;
        }

    }
}
mat-form-field {
    width: 100%
}

// Cards Container
.cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 15px;
    padding: 10px 0;

    // Responsive adjustments
    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 10px;
        padding: 10px 0;
    }

    @media (min-width: 1200px) {
        grid-template-columns: repeat(3, 1fr);
    }
}

// Flip Card Styling
.flip-card {
    height: 340px;
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
    perspective: 1000px;
    // Remove default fuse-widget padding to ensure consistent sizing
    padding: 0 !important;

    // Enhanced 3D flip animation with preserve-3d and smooth transitions
    > div {
        transform-style: preserve-3d;
        transition: transform 0.5s ease-out;
    }

    .fuse-widget-front {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        transition: transform 0.5s ease-out;
        margin: 0 !important;
        padding: 0 !important;

        .card-content {
            height: 100%;
            display: flex;
            flex-direction: column;

            .image-section {
                height: 50%;
                overflow: hidden;
                position: relative;
                cursor: pointer;

                .module-image {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .content-section {
                height: 50%;
                padding: 20px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                .module-title {
                    font-size: 20px;
                    font-weight: 700;
                    color: #002D68;
                    margin: 0 0 8px 0;
                    text-transform: capitalize;
                    letter-spacing: 0.04em;
                }

                .module-description {
                    font-size: 14px;
                    font-weight: 300;
                    color: #666;
                    margin: 0 0 16px 0;
                    line-height: 1.4;
                    flex-grow: 1;
                    overflow: hidden;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                }

                .read-more-btn {
                    background: transparent;
                    border: none;
                    color: #6366f1;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    padding: 0;
                    text-align: left;
                }
            }
        }
    }

    .fuse-widget-back {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        // Override fuse-widget default positioning to match front card exactly
        position: absolute !important;
        top: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        left: 0 !important;
        margin: 0 !important;
        padding: 0 !important;

        .back-content {
            height: 100%;
            padding: 20px;
            display: flex;
            flex-direction: column;
            position: relative;

            .back-btn {
                position: absolute;
                top: 16px;
                right: 16px;
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;

                mat-icon {
                    font-size: 20px;
                    color: #64748b;
                }
            }

            .back-text {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                text-align: center;

                p {
                    font-size: 16px;
                    color: #64748b;
                    margin: 0;
                }
            }
        }
    }
}



.avatar-wrapper {
    position: relative;
    width: 64px;
    height: 64px;
    margin: 0;
}

.avatar {
    width: 64px !important;
    height: 64px !important;
    margin: 0 !important;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-initials {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background-color:#667eea;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 26px;
    font-weight: 600;
    color: white;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    }
}