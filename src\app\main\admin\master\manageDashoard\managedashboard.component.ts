import { Component, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { BaseComponent } from 'app/main/common/base.component';
import { environment } from 'environments/environment';
import { ModuleList } from 'app/dto/countryDto';
import { ModuleApiService } from 'app/services/api/modules.api.service';
import { userIDDto } from 'app/dto/userDto';

@Component({
  selector: 'app-manage-dashboard',
  templateUrl: './managedashboard.component.html',
  styleUrls: ['./managedashboard.component.scss']
})

export class ManageDashoardComponent extends BaseComponent {
  @ViewChild('formUser', { static: true }) ngForm;
  hpbsUrl = environment.hpbsUrl;
  tkn = localStorage.getItem('SSOTOKEN');
  moduleList: ModuleList[];
  roleId: string;
  userName: string;
  picUrl: string;
  showImage: boolean = false;
  flippedCards: { [key: string]: boolean } = {};
  constructor(_snackBar: MatSnackBar, public _dialog: MatDialog, private _router: Router, private _moduleApi: ModuleApiService) {
    super(_snackBar);
  }

  ngOnInit() {
    const isAd = localStorage.getItem('SSOADMIN');
    if (isAd == '1' && this.tkn.trim().length > 0) {
    } else {
      localStorage.setItem('SSOUSERNAME', '');
      localStorage.setItem('SSOUSERPIC', '');
      this._router.navigateByUrl('/login');
    }
    this.userName = localStorage.getItem('SSOUSERNAME');
    this.picUrl = localStorage.getItem('SSOUSERPIC');

    // Check if we should show image initially
    this.showImage = !!(this.picUrl && this.picUrl.trim());
    this.getAllModules();
  }

  getAllModules(): void {
    this.roleId = localStorage.getItem('SSOUSERID');
    const gridDataReq: userIDDto = {
      id: this.roleId,
      UserEmail: ""
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._moduleApi.getUserModules(obj)
      .subscribe(
        (data: any) => {
          this.moduleList = [];
          const objArray = JSON.parse(data.obj.toString());
          for (const _obj of objArray) {
            const _c: ModuleList = {
              moduleId: _obj.moduleId,
              moduleDesc: _obj.moduleDesc,
              URL: _obj.URL,
              Description: _obj.Description
            };
            this.moduleList.push(_c);
          }
        },
        _error => {
        }
      );
  }

  navigateToHousekeeping(url): void {
    window.location.href = url + '/login?token=' + this.tkn;
  }
  /**
     * Handle image load error - show initials instead
     */
  onImageError(): void {
    this.showImage = false;
  }

  /**
   * Toggle card flip state
   */
  toggleCard(moduleId: string): void {
    this.flippedCards[moduleId] = !this.flippedCards[moduleId];
  }

  /**
   * Check if card is flipped
   */
  isCardFlipped(moduleId: string): boolean {
    return this.flippedCards[moduleId] || false;
  }

  /**
   * Handle successful image load
   */
  onImageLoad(): void {
    this.showImage = true;
  }

  /**
   * Get initials from user name (first letter of first name and last name)
   */
  getInitials(): string {
    if (!this.userName) {
      return '??';
    }

    const names = this.userName.trim().split(' ').filter(name => name.length > 0);

    if (names.length === 0) {
      return '??';
    } else if (names.length === 1) {
      // Only one name, take first two characters
      return names[0].substring(0, 2).toUpperCase();
    } else {
      // Take first letter of first name and first letter of last name
      const firstInitial = names[0].charAt(0);
      const lastInitial = names[names.length - 1].charAt(0);
      return (firstInitial + lastInitial).toUpperCase();
    }
  }
}
