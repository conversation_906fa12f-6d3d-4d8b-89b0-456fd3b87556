<div id="forms" class="page-layout simple fullwidth" fxLayout="column">
    <!-- <div class="header p-20 h-20" fxLayout="row" fxLayoutAlign="start center">
        <div fxLayout="column" fxLayoutAlign="center start">
            <div fxLayout="row" fxLayoutAlign="start center">
                <h2>Dashboard</h2>
            </div>
        </div>
    </div> -->
    <div style="display: flex; align-items: center; margin: 30px; justify-content: start; gap: 10px;">
        <div class="avatar-wrapper">
            <img class="avatar" [src]="picUrl" (error)="onImageError()" (load)="onImageLoad()"
                [style.display]="showImage ? 'block' : 'none'">
            <div class="avatar-initials" [style.display]="showImage ? 'none' : 'flex'" [title]="userName">
                {{getInitials()}}
            </div>
        </div>
        <div style="font-size: 32px; font-weight: 900; color: #002D68;">Welcome back, {{userName}}!</div>
    </div>
    <div class="content">
        <div class="cards-container">
            <fuse-widget *ngFor="let module of moduleList" class="flip-card">
                <div class="fuse-widget-front">
                    <div class="card-content">
                        <div class="image-section">
                            <img src='{{ "assets/images/logos/" + module.moduleDesc + ".png" }}'
                                 class="module-image"
                                 (click)="navigateToHousekeeping(module.URL)"
                                 alt="{{module.moduleDesc}}" />
                        </div>
                        <div class="content-section">
                            <h3 class="module-title">{{module.moduleDesc}}</h3>
                            <p class="module-description">{{module.Description}}</p>
                            <button class="read-more-btn" fuseWidgetToggle>
                                Read More
                            </button>
                        </div>
                    </div>
                </div>
                <div class="fuse-widget-back">
                    <div class="back-content">
                        <button class="back-btn" fuseWidgetToggle>
                            <mat-icon>arrow_back</mat-icon>
                        </button>
                        <div class="back-text">
                            <p>Back side content will be displayed here.</p>
                        </div>
                    </div>
                </div>
            </fuse-widget>
        </div>
    </div>
</div>