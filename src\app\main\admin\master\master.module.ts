import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatGridListModule, MatTabsModule } from '@angular/material';
import { FuseSharedModule } from '@fuse/shared.module';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSortHeaderIntl, MatSortModule } from '@angular/material/sort';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialogModule } from '@angular/material/dialog';
import { MatNativeDateModule } from '@angular/material/core';
import { UsermanagementComponent } from './usermanagement/usermanagement.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { AuthGuard } from 'app/guards/auth-guard.service';
import { ManagecompanyComponent } from './managecompany/managecompany.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MAT_DATE_FORMATS } from '@angular/material/core';
import { MY_DATE_FORMATS } from './managecompany/dateformat';
import { ManagefacilityComponent } from './managefacility/managefacility.component';
import { FacilityAdminPipe } from './managefacility/facility-admin.pipe';
import { ManageEmailComponent } from './manageemails/manageemails.component';
import { ManagefacilityadminComponent } from './managefacilityadmin/managefacilityadmin.component';
import { ManageDashoardComponent } from './manageDashoard/managedashboard.component';
import { ManageRoleComponent } from './manage-role/manage-role.component';
import { ManageStaffComponent } from './manage-staff/manage-staff.component';
import { MatRadioModule } from '@angular/material/radio';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NgxIntlTelInputModule } from 'ngx-intl-tel-input';
import { ResignationDateDialogComponent } from './manage-staff/resignation-date-dialog/resignation-date-dialog.component';
import { ConfirmDialogComponent } from './manage-staff/confirm-dialog/confirm-dialog.component';
import { FuseWidgetModule } from '@fuse/components';
import { MatCardModule } from '@angular/material/card';

const routes = [
    {
        path: 'app/user',
        component: UsermanagementComponent,
        canActive: [AuthGuard]
    },
    {
        path: 'app/company',
        component: ManagecompanyComponent,
        canActive: [AuthGuard]
    },
    {
        path: 'app/dashboard',
        component: ManageDashoardComponent,
        canActive: [AuthGuard]
    },
    {
        path: 'app/facility',
        component: ManagefacilityComponent,
        canActive: [AuthGuard]
    },
    {
        path: 'app/facilityAdmin',
        component: ManagefacilityadminComponent,
        canActive: [AuthGuard]
    },
    {
        path: 'app/email',
        component: ManageEmailComponent,
        canActive: [AuthGuard]
    },
    {
        path: 'app/role',
        component: ManageRoleComponent,
        canActive: [AuthGuard]
    },
    {
        path: 'app/staff',
        component: ManageStaffComponent,
        canActive: [AuthGuard]
    }
];

@NgModule({
    declarations: [
        UsermanagementComponent,
        ManagecompanyComponent,
        ManageDashoardComponent,
        ManagefacilityComponent,
        ManageEmailComponent,
        ManagefacilityadminComponent,
        FacilityAdminPipe,
        ManageRoleComponent,
        ManageStaffComponent,
        ResignationDateDialogComponent,
        ConfirmDialogComponent
    ],
    imports: [
        RouterModule.forChild(routes),
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        MatAutocompleteModule,
        MatButtonModule,
        MatCheckboxModule,
        MatDatepickerModule,
        MatNativeDateModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        FuseSharedModule,
        MatToolbarModule,
        MatTableModule,
        MatPaginatorModule,
        MatSortModule,
        MatSnackBarModule,
        MatDialogModule,
        MatExpansionModule,
        MatOptionModule,
        MatSelectModule,
        MatTabsModule,
        MatExpansionModule,
        MatGridListModule,
        MatRadioModule,
        NgxMatSelectSearchModule,
        NgxIntlTelInputModule,
        FuseWidgetModule,
        MatCardModule
    ],
    entryComponents: [
        ResignationDateDialogComponent,
        ConfirmDialogComponent
    ],
    providers: [
        FacilityAdminPipe,
        { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS }
    ]
})
export class MasterModule {
}
